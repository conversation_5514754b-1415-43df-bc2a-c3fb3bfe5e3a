[tool.poetry]
name = "langsmith"
version = "0.4.4"
description = "Client library to connect to the LangSmith LLM Tracing and Evaluation Platform."
authors = ["LangChain <<EMAIL>>"]
license = "MIT"
readme = "README.md"
repository = "https://github.com/langchain-ai/langsmith-sdk"
homepage = "https://smith.langchain.com/"
documentation = "https://docs.smith.langchain.com/"
keywords = [
  "langsmith",
  "langchain",
  "llm",
  "nlp",
  "language",
  "translation",
  "evaluation",
  "tracing",
  "platform",
]
packages = [{ include = "langsmith" }]

[tool.poetry.dependencies]
python = ">=3.9"
pydantic = ">=1,<3"
requests = "^2"
orjson = { version = "^3.9.14", markers = "platform_python_implementation != 'PyPy'" }
httpx = ">=0.23.0,<1"
requests-toolbelt = "^1.0.0"

# Enabled via `langsmith_pyo3` extra: `pip install langsmith[langsmith_pyo3]`.
langsmith-pyo3 = { version = "^0.1.0rc2", optional = true }
zstandard = "^0.23.0"
rich = {version = "^13.9.4", optional = true}
pytest = {version = ">=7.0.0", optional = true}
packaging = ">=23.2"
# Enabled via `openai-agents` extra: `pip install langsmith[openai-agents]`
openai-agents = { version = ">=0.0.3,<0.1", optional = true }

# Enabled via `otel` extra: `pip install langsmith[otel]`
opentelemetry-sdk = {version = "^1.30.0", optional = true}
opentelemetry-api = {version = "^1.30.0", optional = true}
opentelemetry-exporter-otlp-proto-http = {version = "^1.30.0", optional = true}

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
black = ">=23.3,<25.0"
mypy = "^1.9.0"
ruff = "^0.6.9"
types-requests = "^********"
pandas-stubs = "^2.0.1.230501"
types-pyyaml = "^*********"
pytest-asyncio = "^0.21.0"
types-psutil = "^********"
psutil = "^5.9.5"
freezegun = "^1.2.2"
pytest-subtests = "^0.11.0"
pytest-watcher = { version = "^0.3.4", python = "<4.0" }
pytest-xdist = "^3.5.0"
pytest-cov = "^4.1.0"
dataclasses-json = { version = "^0.6.4", python = "<4.0" }
types-tqdm = "^4.66.0.20240106"
vcrpy = ">=7.0.0"
fastapi = "^0.115.4"
uvicorn = "^0.29.0"
pytest-rerunfailures = "^14.0"
pytest-socket = { version = "^0.7.0", python = "<4.0" }
pyperf = "^2.7.0"
py-spy = "^0.3.14"
multipart = "^1.0.0"
# Hack to get tests/integration_tests/test_context_propagation.py to pass
# TODO: remove
httpx = ">=0.23.0,<0.28.0"
rich = "^13.9.4"
pytest-retry = "^1.7.0"
pytest-dotenv = "^0.5.2"
opentelemetry-sdk = "^1.34.1"
opentelemetry-exporter-otlp-proto-http = "^1.34.1"
opentelemetry-api = "^1.34.1"

[tool.poetry.group.lint.dependencies]
openai = "^1.55"


[tool.poetry.group.test.dependencies]
pytest-socket = { version = "^0.7.0", python = "<4.0" }
anthropic = "^0.45.0"

[tool.poetry.extras]
vcr = ["vcrpy"]
langsmith_pyo3 = ["langsmith-pyo3"]
otel = ["opentelemetry-api", "opentelemetry-sdk", "opentelemetry-exporter-otlp-proto-http"]
pytest = ["pytest", "rich", "vcrpy"]
openai-agents = ["openai-agents"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
lint.select = [
  "E",    # pycodestyle
  "F",    # pyflakes
  "I",    # isort
  "D",    # pydocstyle
  "D401", # First line should be in imperative mood
  "T201",
  "UP",
]
lint.ignore = [
  "UP007",
  # Relax the convention by _not_ requiring documentation for every function parameter.
  "D417",
]
target-version = "py39"

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.per-file-ignores]
"langsmith/run_helpers.py" = ["E501"]
"docs/conf.py" = ["E501"]
"langsmith/cli/*" = ["T201", "D", "UP"]
"docs/create_api_rst.py" = ["D101", "D103", "E501"]
"docs/scripts/custom_formatter.py" = ["D100"]
"langsmith/anonymizer.py" = ["E501"]
"langsmith/async_client.py" = ["E501"]
"langsmith/client.py" = ["E501"]
"langsmith/schemas.py" = ["E501"]
"tests/evaluation/__init__.py" = ["E501"]
"tests/unit_tests/test_client.py" = ["E501"]
"tests/*" = ["D", "UP"]
"bench/*" = ["D", "UP", "T"]
"docs/*" = ["T", "D"]

[tool.ruff.format]
docstring-code-format = true
docstring-code-line-length = 80

[tool.mypy]
plugins = ["pydantic.v1.mypy", "pydantic.mypy"]
ignore_missing_imports = "True"
disallow_untyped_defs = "True"

[tool.pytest.ini_options]
asyncio_mode = "auto"
markers = ["slow: long-running tests"]

[project]
name = "langsmith"
dynamic = ["version"]

[tool.poetry.plugins."pytest11"]
langsmith_plugin = "langsmith.pytest_plugin"

[tool.poetry_bumpversion.file."langsmith/__init__.py"]
search = '__version__ = "{current_version}"'
replace = '__version__ = "{new_version}"'
