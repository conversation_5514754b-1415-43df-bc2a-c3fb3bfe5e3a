#!/usr/bin/env python3
"""
Test nested spans in remote graph to verify correct hierarchy preservation.
"""

import uuid
from datetime import datetime, timezone
from langsmith import run_trees


def test_nested_remote_graph():
    """Test that nested spans within a remote graph maintain correct hierarchy."""
    print("=== Testing Nested Remote Graph ===")
    
    # Create a complex hierarchy: grandparent -> parent -> child -> grandchild
    grandparent = run_trees.RunTree(
        name="grandparent",
        run_type="chain",
        project_name="original_project",
        replicas=[
            ("remote_project", {"remote-graph": True}),
            ("normal_replica", None)
        ]
    )
    
    parent = grandparent.create_child(name="parent", run_type="chain")
    child = parent.create_child(name="child", run_type="tool")
    grandchild = child.create_child(name="grandchild", run_type="llm")
    
    print("Original hierarchy:")
    print(f"Grandparent: {grandparent.dotted_order}")
    print(f"Parent: {parent.dotted_order}")
    print(f"Child: {child.dotted_order}")
    print(f"Grandchild: {grandchild.dotted_order}")
    
    print("\n--- Normal Replica (should maintain full hierarchy) ---")
    gp_normal = grandparent._remap_for_project("normal_replica", None)
    p_normal = parent._remap_for_project("normal_replica", None)
    c_normal = child._remap_for_project("normal_replica", None)
    gc_normal = grandchild._remap_for_project("normal_replica", None)
    
    print(f"Grandparent normal - Trace: {gp_normal['trace_id']}, Parent: {gp_normal.get('parent_run_id')}")
    print(f"Parent normal - Trace: {p_normal['trace_id']}, Parent: {p_normal.get('parent_run_id')}")
    print(f"Child normal - Trace: {c_normal['trace_id']}, Parent: {c_normal.get('parent_run_id')}")
    print(f"Grandchild normal - Trace: {gc_normal['trace_id']}, Parent: {gc_normal.get('parent_run_id')}")
    
    print("\n--- Remote Graph (current implementation) ---")
    gp_remote = grandparent._remap_for_project("remote_project", {"remote-graph": True})
    p_remote = parent._remap_for_project("remote_project", {"remote-graph": True})
    c_remote = child._remap_for_project("remote_project", {"remote-graph": True})
    gc_remote = grandchild._remap_for_project("remote_project", {"remote-graph": True})
    
    print(f"Grandparent remote - Trace: {gp_remote['trace_id']}, Parent: {gp_remote.get('parent_run_id')}")
    print(f"Parent remote - Trace: {p_remote['trace_id']}, Parent: {p_remote.get('parent_run_id')}")
    print(f"Child remote - Trace: {c_remote['trace_id']}, Parent: {c_remote.get('parent_run_id')}")
    print(f"Grandchild remote - Trace: {gc_remote['trace_id']}, Parent: {gc_remote.get('parent_run_id')}")
    
    print(f"\nGrandparent remote dotted: {gp_remote.get('dotted_order')}")
    print(f"Parent remote dotted: {p_remote.get('dotted_order')}")
    print(f"Child remote dotted: {c_remote.get('dotted_order')}")
    print(f"Grandchild remote dotted: {gc_remote.get('dotted_order')}")
    
    # Check if the issue exists
    print("\n--- Analysis ---")
    
    # All should have the same trace_id (the root of the remote graph)
    print(f"All trace IDs same? {gp_remote['trace_id'] == p_remote['trace_id'] == c_remote['trace_id'] == gc_remote['trace_id']}")
    
    # Only grandparent should have no parent
    print(f"Grandparent has no parent? {gp_remote.get('parent_run_id') is None}")
    print(f"Parent has no parent? {p_remote.get('parent_run_id') is None}")
    print(f"Child has no parent? {c_remote.get('parent_run_id') is None}")
    print(f"Grandchild has no parent? {gc_remote.get('parent_run_id') is None}")
    
    # Check dotted order structure
    gp_dotted = gp_remote.get('dotted_order', '')
    p_dotted = p_remote.get('dotted_order', '')
    c_dotted = c_remote.get('dotted_order', '')
    gc_dotted = gc_remote.get('dotted_order', '')
    
    print(f"Grandparent dotted segments: {gp_dotted.count('.') + 1 if gp_dotted else 0}")
    print(f"Parent dotted segments: {p_dotted.count('.') + 1 if p_dotted else 0}")
    print(f"Child dotted segments: {c_dotted.count('.') + 1 if c_dotted else 0}")
    print(f"Grandchild dotted segments: {gc_dotted.count('.') + 1 if gc_dotted else 0}")
    
    print("\n✅ ANALYSIS RESULTS:")

    # Verify the correct behavior
    all_same_trace = gp_remote['trace_id'] == p_remote['trace_id'] == c_remote['trace_id'] == gc_remote['trace_id']
    gp_is_root = gp_remote.get('parent_run_id') is None
    hierarchy_preserved = (
        p_remote.get('parent_run_id') == gp_remote['id'] and
        c_remote.get('parent_run_id') == p_remote['id'] and
        gc_remote.get('parent_run_id') == c_remote['id']
    )

    print(f"✅ All runs share same trace ID: {all_same_trace}")
    print(f"✅ Grandparent is root (no parent): {gp_is_root}")
    print(f"✅ Hierarchy preserved within remote graph: {hierarchy_preserved}")

    if all_same_trace and gp_is_root and hierarchy_preserved:
        print("\n🎉 SUCCESS: Remote graph implementation is working correctly!")
        print("   - Grandparent becomes the root of the remote graph")
        print("   - All descendants maintain their relative hierarchy")
        print("   - Connection to original parent graph is truncated")
    else:
        print("\n❌ ISSUE: Remote graph implementation has problems")


def test_middle_child_remote_graph():
    """Test remote-graph starting from a middle child (not the root)."""
    print("\n=== Testing Remote Graph Starting from Middle Child ===")

    # Create hierarchy: root -> middle -> child -> grandchild
    # But only middle and its descendants should be in remote graph
    root = run_trees.RunTree(
        name="root",
        run_type="chain",
        project_name="original_project",
        replicas=[("normal_replica", None)]  # Root not in remote graph
    )

    middle = root.create_child(
        name="middle",
        run_type="chain"
    )
    # Set replicas on middle to include remote-graph
    middle.replicas = [
        ("remote_project", {"remote-graph": True}),
        ("normal_replica", None)
    ]

    child = middle.create_child(name="child", run_type="tool")
    # Child should also have remote-graph (inherited or explicit)
    child.replicas = [
        ("remote_project", {"remote-graph": True}),
        ("normal_replica", None)
    ]

    grandchild = child.create_child(name="grandchild", run_type="llm")
    # Grandchild should also have remote-graph (inherited or explicit)
    grandchild.replicas = [
        ("remote_project", {"remote-graph": True}),
        ("normal_replica", None)
    ]

    print("Original hierarchy:")
    print(f"Root: {root.dotted_order}")
    print(f"Middle: {middle.dotted_order}")
    print(f"Child: {child.dotted_order}")
    print(f"Grandchild: {grandchild.dotted_order}")

    print("\n--- Debug: Parent Run IDs ---")
    print(f"Middle parent_run_id: {middle.parent_run_id}")
    print(f"Child parent_run_id: {child.parent_run_id}")
    print(f"Grandchild parent_run_id: {grandchild.parent_run_id}")

    print("\n--- Remote Graph Results ---")
    # Only middle and descendants should be in remote graph
    middle_remote = middle._remap_for_project("remote_project", {"remote-graph": True})
    child_remote = child._remap_for_project("remote_project", {"remote-graph": True})
    grandchild_remote = grandchild._remap_for_project("remote_project", {"remote-graph": True})

    print(f"Middle remote - Trace: {middle_remote['trace_id']}, Parent: {middle_remote.get('parent_run_id')}")
    print(f"Child remote - Trace: {child_remote['trace_id']}, Parent: {child_remote.get('parent_run_id')}")
    print(f"Grandchild remote - Trace: {grandchild_remote['trace_id']}, Parent: {grandchild_remote.get('parent_run_id')}")

    print(f"\nMiddle remote dotted: {middle_remote.get('dotted_order')}")
    print(f"Child remote dotted: {child_remote.get('dotted_order')}")
    print(f"Grandchild remote dotted: {grandchild_remote.get('dotted_order')}")

    # Verify correct behavior
    all_same_trace = middle_remote['trace_id'] == child_remote['trace_id'] == grandchild_remote['trace_id']
    middle_is_root = middle_remote.get('parent_run_id') is None
    hierarchy_preserved = (
        child_remote.get('parent_run_id') == middle_remote['id'] and
        grandchild_remote.get('parent_run_id') == child_remote['id']
    )

    print(f"\n✅ All runs share same trace ID: {all_same_trace}")
    print(f"✅ Middle becomes root (no parent): {middle_is_root}")
    print(f"✅ Hierarchy preserved within remote graph: {hierarchy_preserved}")

    if all_same_trace and middle_is_root and hierarchy_preserved:
        print("\n🎉 SUCCESS: Middle child remote graph working correctly!")
    else:
        print("\n❌ ISSUE: Middle child remote graph has problems")


def test_what_should_happen():
    """Show what the correct behavior should be."""
    print("\n=== What Should Happen ===")
    print("For a hierarchy: original_root -> child1 -> child2 -> child3")
    print("With remote-graph starting at child1:")
    print("")
    print("Original project:")
    print("  original_root (trace_id=original_root, parent=None)")
    print("  ├── child1 (trace_id=original_root, parent=original_root)")
    print("  │   ├── child2 (trace_id=original_root, parent=child1)")
    print("  │   └── child3 (trace_id=original_root, parent=child2)")
    print("")
    print("Remote project (with remote-graph at child1):")
    print("  child1 (trace_id=child1_remote, parent=None)  ← NEW ROOT")
    print("  ├── child2 (trace_id=child1_remote, parent=child1_remote)")
    print("  └── child3 (trace_id=child1_remote, parent=child2_remote)")
    print("")
    print("Key insight: We need to identify the 'child root' and preserve")
    print("hierarchy WITHIN the remote graph while truncating the connection")
    print("to the original parent graph.")


if __name__ == "__main__":
    test_nested_remote_graph()
    test_middle_child_remote_graph()
    test_what_should_happen()
