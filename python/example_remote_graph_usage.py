#!/usr/bin/env python3
"""
Example usage of the remote-graph feature for distributed tracing.

This example demonstrates how to use remote-graph to create standalone traces
in child services while maintaining full trace hierarchy in the parent service.
"""

import langsmith as ls
from langsmith import traceable, tracing_context


# Example 1: Using tracing_context with remote-graph
def example_tracing_context():
    """Example using tracing_context with remote-graph replicas."""
    print("=== Example 1: Using tracing_context with remote-graph ===")
    
    # Set up tracing context with remote-graph replica
    with tracing_context(
        project_name="parent_service",
        replicas=[
            ("child_service_project", {"remote-graph": True}),
            ("monitoring_project", None)  # Normal replica
        ]
    ):
        
        @traceable(name="parent_operation")
        def parent_function():
            print("Executing parent operation...")
            
            @traceable(name="child_operation")
            def child_function():
                print("Executing child operation...")
                return "child_result"
            
            result = child_function()
            return f"parent_result: {result}"
        
        result = parent_function()
        print(f"Result: {result}")
    
    print("✅ Tracing context example completed")


# Example 2: Distributed tracing with remote-graph
def example_distributed_tracing():
    """Example of distributed tracing with remote-graph."""
    print("\n=== Example 2: Distributed tracing with remote-graph ===")
    
    # Simulate parent service creating a trace
    with tracing_context(
        project_name="parent_service",
        replicas=[("child_service", {"remote-graph": True})]
    ):
        
        @traceable(name="api_request")
        def parent_service_handler():
            print("Parent service handling request...")
            
            # Get headers to pass to child service
            current_run = ls.get_current_run_tree()
            if current_run:
                headers = current_run.to_headers()
                print(f"Headers to pass to child: {list(headers.keys())}")
                
                # Simulate calling child service
                child_service_handler(headers)
            
            return "parent_response"
        
        def child_service_handler(headers):
            """Simulate child service receiving headers."""
            print("Child service receiving headers...")
            
            # Child service creates its own trace from headers
            @traceable(name="child_service_operation")
            def child_operation(langsmith_extra=None):
                print("Child service executing operation...")
                return "child_response"
            
            # Pass headers as parent for distributed tracing
            result = child_operation(langsmith_extra={"parent": headers})
            print(f"Child service result: {result}")
        
        result = parent_service_handler()
        print(f"Parent service result: {result}")
    
    print("✅ Distributed tracing example completed")


# Example 3: Manual RunTree creation with remote-graph
def example_manual_runtree():
    """Example of manually creating RunTree with remote-graph."""
    print("\n=== Example 3: Manual RunTree creation with remote-graph ===")
    
    # Create parent run with remote-graph replica
    parent_run = ls.RunTree(
        name="manual_parent",
        run_type="chain",
        project_name="main_project",
        replicas=[
            ("analytics_project", {"remote-graph": True, "tags": ["analytics"]}),
            ("backup_project", None)
        ]
    )
    
    # Create child run (inherits replicas)
    child_run = parent_run.create_child(
        name="manual_child",
        run_type="tool"
    )
    
    # Demonstrate the difference in remapping
    print("Parent run remapping:")
    normal_parent = parent_run._remap_for_project("backup_project", None)
    remote_parent = parent_run._remap_for_project("analytics_project", {"remote-graph": True, "tags": ["analytics"]})
    
    print(f"  Normal replica - Trace ID: {normal_parent['trace_id']}")
    print(f"  Remote graph - Trace ID: {remote_parent['trace_id']}")
    print(f"  Remote graph - Parent ID: {remote_parent.get('parent_run_id')}")
    
    print("\nChild run remapping:")
    normal_child = child_run._remap_for_project("backup_project", None)
    remote_child = child_run._remap_for_project("analytics_project", {"remote-graph": True, "tags": ["analytics"]})
    
    print(f"  Normal replica - Trace ID: {normal_child['trace_id']}")
    print(f"  Normal replica - Parent ID: {normal_child.get('parent_run_id')}")
    print(f"  Remote graph - Trace ID: {remote_child['trace_id']}")
    print(f"  Remote graph - Parent ID: {remote_child.get('parent_run_id')}")
    
    # Verify remote graph creates standalone trace
    assert remote_child.get('parent_run_id') is None
    assert remote_child['trace_id'] == remote_child['id']
    
    print("✅ Manual RunTree example completed")


# Example 4: Real-world microservices scenario
def example_microservices():
    """Example of microservices using remote-graph."""
    print("\n=== Example 4: Microservices scenario ===")
    
    # API Gateway service
    with tracing_context(
        project_name="api_gateway",
        replicas=[
            ("user_service", {"remote-graph": True}),
            ("order_service", {"remote-graph": True}),
            ("shared_monitoring", None)  # Full trace for monitoring
        ]
    ):
        
        @traceable(name="handle_user_request")
        def api_gateway():
            print("API Gateway: Processing user request")
            
            # Call user service (will create standalone trace in user_service project)
            user_data = user_service_call()
            
            # Call order service (will create standalone trace in order_service project)
            order_data = order_service_call(user_data)
            
            return {"user": user_data, "order": order_data}
        
        @traceable(name="user_service_call")
        def user_service_call():
            print("User Service: Fetching user data")
            return {"user_id": 123, "name": "John Doe"}
        
        @traceable(name="order_service_call")
        def order_service_call(user_data):
            print("Order Service: Processing order")
            return {"order_id": 456, "user_id": user_data["user_id"]}
        
        result = api_gateway()
        print(f"Final result: {result}")
    
    print("✅ Microservices example completed")


if __name__ == "__main__":
    print("🚀 Remote Graph Feature Examples")
    print("=" * 50)
    
    example_tracing_context()
    example_distributed_tracing()
    example_manual_runtree()
    example_microservices()
    
    print("\n" + "=" * 50)
    print("🎉 All examples completed successfully!")
    print("\nKey benefits of remote-graph:")
    print("• Parent services maintain full trace hierarchy")
    print("• Child services get standalone traces in their projects")
    print("• No broken references or ingestion failures")
    print("• Each service can analyze their traces independently")
    print("• Monitoring systems can still see the full distributed trace")